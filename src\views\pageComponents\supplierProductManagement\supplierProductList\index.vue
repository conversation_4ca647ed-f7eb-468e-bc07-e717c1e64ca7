<template>
  <div class="main">
    <div class="flex justify-between mb-12">
      <div class="flex items-center justify-between w-full">
        <a-tabs v-if="permissionsLoaded" class="filterTabs w-full" v-model:active-key="otherParams.status" @change="handleFilterScopeChange" :key="tabsKey">
          <a-tab-pane v-if="btnPermission[91100]" :key="null">
            <template #tab>
              <a-badge :count="count?.total_count" :offset="[12, -2]" :overflowCount="100000">
                <span>全部</span>
              </a-badge>
            </template>
          </a-tab-pane>
          <a-tab-pane v-if="btnPermission[91200]" :key="0" value="0">
            <template #tab>
              <a-badge :count="count?.waiting_count" :offset="[12, -2]" :overflowCount="100000">
                <span>待选商品</span>
              </a-badge>
            </template>
          </a-tab-pane>
          <a-tab-pane v-if="btnPermission[91300]" :key="1" value="1">
            <template #tab>
              <a-badge :count="count?.audit_count" :offset="[12, -2]" :overflowCount="100000">
                <span>选品审核中</span>
              </a-badge>
            </template>
          </a-tab-pane>
          <a-tab-pane v-if="btnPermission[91500]" :key="3" value="3">
            <template #tab>
              <a-badge :count="count?.completed_count" :offset="[12, -2]" :overflowCount="100000">
                <span>完成选品</span>
              </a-badge>
            </template>
          </a-tab-pane>
          <a-tab-pane v-if="btnPermission[91400]" :key="2" value="2">
            <template #tab>
              <a-badge :count="count?.development_count" :offset="[12, -2]" :overflowCount="100000">
                <span>已驳回</span>
              </a-badge>
            </template>
          </a-tab-pane>
          <a-tab-pane v-if="btnPermission[91600]" :key="4" value="4">
            <template #tab>
              <a-badge :count="count?.rejected_count" :offset="[12, -2]" :overflowCount="100000">
                <span>已拒绝</span>
              </a-badge>
            </template>
          </a-tab-pane>
        </a-tabs>
        <div v-else class="flex justify-center items-center h-12">
          <span class="text-gray-500">加载中...</span>
        </div>
      </div>
      <a-space>
        <div v-show="otherParams.status === 0" class="time-filter-container">
          <span class="text-black">修改时间</span>
          <QuickTimeSelection ref="quickTimeSelectionRef" v-model:value="otherParams.timeScope" @change="changeTimeScope" />
          <a-range-picker v-model:value="dateRange" value-format="YYYY-MM-DD" @change="changeTimeRange" @focus="foucsTimeRange" />
        </div>
      </a-space>
    </div>
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.SUPPLIER_PRODUCT_LIBRARY" @search="search" @setting="tableRef?.showTableSetting()" :clearCb="clearFilter"></SearchForm>
    <BaseTable
      :key="baseTableRenderKey"
      ref="tableRef"
      :isCheckbox="true"
      :pageType="PageType.SUPPLIER_PRODUCT_LIBRARY"
      :get-list="getListFn"
      v-model:form="formArr"
      @init-finish="handleInitFinish"
      :is-index="true"
    >
      <template #right-btn>
        <a-select v-model:value="exportSelectValue" class="w-120px" :disabled="!btnPermission[otherParams.status === 0 ? 91202 : 91102]">
          <a-select-option v-for="item in exportSelect" :key="item.value" :value="item.value" :label="item.label">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-button @click="handleExport" class="btn" :disabled="!btnPermission[otherParams.status === 0 ? 91202 : 91102]">导出</a-button>
      </template>
      <template #images_view_url="{ row }">
        <div class="flex items-center justify-center w-40 h-40">
          <a-image
            :src="row.images_view_url"
            :class="lineHeightType == 1 ? '!w-40 !h-30' : lineHeightType == 2 ? '!w-50 !h-40' : '!w-70 !h-40'"
            :preview="{
              onVisibleChange: (visible: boolean) => setPreviewVisible(row, visible),
              src: row.bigImagesUrl,
            }"
          >
            <template #previewMask>
              <EyeOutlined />
            </template>
          </a-image>
        </div>
      </template>
      <template #supplier_product_number="{ row }">
        <copy-btn v-copy="row.supplier_product_number" v-if="row.supplier_product_number" />
        <span>{{ row.supplier_product_number || '--' }}</span>
      </template>
      <template #product_number="{ row }">
        <copy-btn v-copy="row.product_number" v-if="row.product_number" />
        <span>{{ row.product_number || '--' }}</span>
      </template>
      <template #supplier_number="{ row }">
        <copy-btn v-copy="row.supplier_number" v-if="row.supplier_number" />
        <span>{{ row.supplier_number || '--' }}</span>
      </template>
      <template #operate="{ row }">
        <div class="operation-buttons">
          <!-- 查看按钮 - 所有标签页都有 -->
          <a-button type="text" @click="handleView(row.id)" :disabled="!btnPermission[getCurrentPermission('view')]">查看</a-button>

          <!-- 待选商品标签页的操作按钮 -->
          <template v-if="otherParams.status === 0">
            <a-button type="text" @click="handleSubmitAudit(row.id)" :disabled="!btnPermission[getCurrentPermission('submit_audit')]">提审</a-button>
            <a-button
              type="text"
              @click="handleOverrule(row.id)"
              :disabled="!btnPermission[getCurrentPermission('overrule')]"
              class="red-text-button"
              style="--ant-primary-color: #d33333"
            >
              驳回
            </a-button>
            <a-button
              type="text"
              @click="handleRefuse(row.id)"
              :disabled="!btnPermission[getCurrentPermission('refuse')]"
              class="red-text-button"
              style="--ant-primary-color: #d33333"
            >
              拒绝选品
            </a-button>
          </template>
        </div>
      </template>
      <!-- <template #product_license="{ row }">
        <a-button type="link" @click="handleViewLicense(row.id, 'license')">
          <span class="underline">1</span>
        </a-button>
      </template> -->
      <!-- <template #product_certificate="{ row }">
        <a-button type="link" @click="handleViewLicense(row.id, 'certificate')">
          <span class="underline">2</span>
        </a-button>
      </template> -->
    </BaseTable>
    <ProductViewDrawer v-if="visible" v-model:visible="visible" :id="selectedId" :isEdit="false" />
    <RejectModal ref="rejectModalRef" @success="handleOperationSuccess" />
    <SubmitAuditModal ref="submitAuditModalRef" @success="handleOperationSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import { message } from 'ant-design-vue'
import { EyeOutlined } from '@ant-design/icons-vue'
import { PageType } from '@/common/enum'
import { GetProductPreviewUrl, GetCommonOption } from '@/servers/Common'
import BaseTable from '@/components/BaseTable/index.vue'
import { GetList, GetCategoryOption } from '@/servers/SupplierProduceStock'
import { getCommonOption } from '@/utils'
import { Add as AddDownloadTask } from '@/servers/DownloadCenter'
import QuickTimeSelection from '@/components/QuickTimeSelection.vue'
import { usePermission } from '@/hook/usePermission'
import CopyBtn from '@/components/CopyBtn/index.vue'
import { pageTableConfig } from '@/common/pageTableConfig'
import useBadgeStore from '@/store/modules/badgeStore'
import ProductViewDrawer from './components/ProductViewDrawer.vue'
import RejectModal from './components/RejectModal.vue'
import SubmitAuditModal from './components/SubmitAuditModal.vue'
import { getCurrentTabConfig, generateFormArrByStatus } from './config/tabConfig'

const badgeStore = useBadgeStore()
const { btnPermission } = usePermission()

// 确保formatter配置正确的函数
const ensureFormatterConfig = (columns: any[]) => {
  const defaultConfig = pageTableConfig[PageType.SUPPLIER_PRODUCT_LIBRARY]
  columns.forEach((column) => {
    const defaultColumn = defaultConfig.find((def) => def.key === column.key)
    if (defaultColumn && defaultColumn.formatter && !column.formatter) {
      column.formatter = defaultColumn.formatter
      console.log(`[Formatter] Added formatter for column: ${column.key}`)
    }
  })
  return columns
}

const visible = ref(false)
const selectedId = ref()

const tableKey = ref<any[]>([])
// 保存原始完整的表格列配置，用于标签页切换时的基准计算
const originalTableKey = ref<any[]>([])

// Debug controls and helpers
const DEBUG = false
const FORCE_REMOUNT_ON_SWITCH = true // 启用每次切换都重新加载，确保一致性
const FORCE_REMOUNT_ON_VXE_FAIL = true
const baseTableRenderKey = ref(0)
const log = (...args: any[]) => {
  if (DEBUG) console.log(...args)
}
const logKeys = (label: string, arr?: any[]) => {
  if (!DEBUG) return
  try {
    const keys = (arr || []).map((x) => x?.key)
    console.log(`[Columns] ${label} (len=${arr?.length ?? 0})`, keys)
  } catch (e) {
    console.log(`[Columns] ${label} (log error)`, e)
  }
}
const logCols = (label: string, arr?: any[]) => {
  if (!DEBUG) return
  try {
    console.log(
      `[Columns:Detail] ${label}`,
      (arr || []).map((x) => ({ key: x?.key, index: x?.index, is_show: x?.is_show, width: x?.width, freeze: x?.freeze })),
    )
  } catch (e) {
    console.log(`[Columns:Detail] ${label} (log error)`, e)
  }
}
const logVxe = (label: string) => {
  if (!DEBUG) return
  try {
    const full = tableRef.value?.tableRef?.getTableColumn?.().fullColumn || []
    logKeys(`${label} vxe.fullColumn`, full)
    return full
  } catch (e) {
    console.warn('[Columns] getTableColumn error', e)
    return []
  }
}

// 强制同步 vxe-table 列配置的工具函数
const forceVxeColumnSync = (targetColumns: any[]) => {
  if (!DEBUG) return false
  try {
    const $table = tableRef.value?.tableRef
    if (!$table) {
      log('[VXE] No table reference available')
      return false
    }

    // 尝试获取 vxe-table 的内部列配置
    const tableColumn = $table.getTableColumn?.()
    if (!tableColumn || !tableColumn.fullColumn) {
      log('[VXE] No tableColumn or fullColumn available')
      return false
    }

    const fullColumn = tableColumn.fullColumn
    log('[VXE] fullColumn check:', fullColumn.length, 'first item:', fullColumn[0])

    // 修正检测逻辑：检查第一个元素是否真的是有效对象
    const isValidColumn = fullColumn && fullColumn.length > 0 && fullColumn[0] !== undefined && fullColumn[0] !== null && typeof fullColumn[0] === 'object' && fullColumn[0].field !== undefined

    if (!isValidColumn) {
      log('[VXE] fullColumn not ready - items are undefined or invalid')
      return false
    }

    log('[VXE] Syncing column configuration...')
    log(
      '[VXE] Target columns:',
      targetColumns.map((c) => c.key),
    )
    log(
      '[VXE] VXE columns:',
      fullColumn.map((c) => c.field),
    )

    // 同步列的顺序、可见性和宽度
    let syncCount = 0
    targetColumns.forEach((configCol: any, index: number) => {
      const vxeCol = fullColumn.find((col: any) => col.field === configCol.key)
      if (vxeCol) {
        // 更新列的属性
        vxeCol.visible = configCol.is_show !== false
        if (configCol.width) {
          vxeCol.width = configCol.width
        }
        // 尝试更新列的顺序（如果 vxe-table 支持）
        if (vxeCol.index !== undefined) {
          vxeCol.index = index
        }
        syncCount++
      } else {
        log(`[VXE] Column not found in vxe: ${configCol.key}`)
      }
    })

    // 刷新列显示
    $table.refreshColumn()
    log(`[VXE] Column sync completed ✓ (${syncCount}/${targetColumns.length} columns synced)`)
    return syncCount > 0
  } catch (e) {
    console.error('[VXE] forceVxeColumnSync failed:', e)
    return false
  }
}

const quickTimeSelectionRef = ref<any>()
const dateRange = ref<any[]>([])

// 弹窗组件引用
const rejectModalRef = ref<any>()
const submitAuditModalRef = ref<any>()

const count = ref({
  total_count: 0,
  waiting_count: 0,
  audit_count: 0,
  development_count: 0,
  completed_count: 0,
  rejected_count: 0,
})

// 移除 filterUpdateKey，改用直接更新 formArr 的方式
// const filterUpdateKey = ref(0)

// 搜索功能
const search = () => {
  console.log('🔍 [search] 搜索被触发，当前formArr状态:')
  formArr.value.forEach((item: any) => {
    if (item.key === 'brand_id_list') {
      console.log('🔍 [search] 品牌筛选项状态:', {
        key: item.key,
        label: item.label,
        value: item.value,
        multiple: item.multiple,
        optionsLength: item.options?.length,
        firstFewOptions: item.options?.slice(0, 3), // 只显示前3个选项避免日志过长
        selectedOption: item.options?.find((opt: any) => item.value?.includes(opt.value)),
      })
    }
  })
  tableRef.value.search()
}

const lineHeightType = computed(() => {
  return tableRef.value.lineHeightType
})

const getListFn = (obj: any) => {
  // 确保 brand_id_list 始终是数组，并且数组中的值是数字类型
  const brandIdListItem = formArr.value.find((item: any) => item.key === 'brand_id_list')

  console.log('🔍 [getListFn] 处理前的品牌筛选项:', {
    found: !!brandIdListItem,
    value: brandIdListItem?.value,
    type: typeof brandIdListItem?.value,
    isArray: Array.isArray(brandIdListItem?.value),
  })

  if (brandIdListItem) {
    if (!brandIdListItem.value) {
      brandIdListItem.value = []
    } else if (Array.isArray(brandIdListItem.value)) {
      // 如果已经是数组，确保数组中的值都是数字类型
      brandIdListItem.value = brandIdListItem.value
        .map((id: any) => {
          return typeof id === 'string' ? parseInt(id) : Number(id)
        })
        .filter((id: number) => !Number.isNaN(id))
    } else {
      // 如果是字符串或其他类型，转换为包含数字的数组
      const numValue = typeof brandIdListItem.value === 'string' ? parseInt(brandIdListItem.value) : Number(brandIdListItem.value)
      brandIdListItem.value = !Number.isNaN(numValue) ? [numValue] : []
    }

    console.log('🔍 [getListFn] 处理后的品牌筛选项:', {
      value: brandIdListItem.value,
      type: typeof brandIdListItem.value,
      isArray: Array.isArray(brandIdListItem.value),
    })
  }

  const params = {
    ...obj,
    is_page: true,
    is_get_total: true,
    is_get_total_only: false,
  }

  // 确保 params 中的 brand_id_list 使用处理后的数组值
  if (brandIdListItem && brandIdListItem.value) {
    params.brand_id_list = brandIdListItem.value
  }

  // 根据不同的标签页设置不同的状态参数
  const currentStatus = otherParams.value.status

  if (currentStatus === null) {
    // 全部标签页：不设置状态筛选，显示所有数据
    // params 不需要添加 status 参数
  } else if (currentStatus === 0) {
    // 待选商品标签页：status = 0 (待选品)
    params.selection_status = 0
    params.modified_at_start = otherParams.value.modified_at_start
    params.modified_at_end = otherParams.value.modified_at_end
  } else if (currentStatus === 1) {
    // 选品审核中标签页：status = 10 (选品审核中)
    params.selection_status = 10
  } else if (currentStatus === 2) {
    // 已驳回标签页：status = 50 (已驳回)
    params.selection_status = 50
  } else if (currentStatus === 3) {
    // 完成选品标签页：status = 40(完成选品)
    params.selection_status = 40
  } else if (currentStatus === 4) {
    // 已拒绝标签页：status = 60(已拒绝)
    params.selection_status = 60
  }

  // 添加审核结果筛选（从formArr中获取）
  const auditResultValue = formArr.value.find((item: any) => item.key === 'status')?.value
  if (auditResultValue) {
    params.status = auditResultValue
  }
  // 处理商品类目参数，将级联选择器的数组数组转换为叶子节点ID数组
  if (params.category_id_list && Array.isArray(params.category_id_list)) {
    const leafIds: number[] = []
    params.category_id_list.forEach((path: number[]) => {
      if (path && path.length > 0) {
        // 取每个路径的最后一个ID（叶子节点）
        leafIds.push(path[path.length - 1])
      }
    })
    params.category_id_list = leafIds
  }

  return GetList(params)
}

// 商品类目下拉数据
const categoryOptions = ref<any[]>([])

const otherParams = ref({
  status: null as number | null, // 初始值保持null，但会在权限加载后自动调整
  timeScope: 0,
  modified_at_start: '',
  modified_at_end: '',
})

// 用于强制重新渲染标签页组件
const tabsKey = ref(0)

// 权限是否已加载
const permissionsLoaded = ref(false)

const exportSelectValue = ref(1)
// 导出下拉框
const exportSelect = ref([
  {
    label: '导出全部',
    value: 1,
  },
  {
    label: '导出所选',
    value: 2,
  },
  {
    label: '导出筛选条件',
    value: 3,
  },
])

// // 许可证 资质证书ref
// const licenseRef = ref<any>()
// 表格和表单引用
const tableRef = ref()
// 搜索表单配置 - 使用新的动态生成方式
const formArr: any = ref(generateFormArrByStatus(otherParams.value.status))

const handleView = (id: number) => {
  selectedId.value = id
  visible.value = true
}

// 获取当前标签页的权限ID
const getCurrentPermission = (operation: string) => {
  const config = getCurrentTabConfig(otherParams.value.status)
  return config.operationConfig.permissionMap[operation] || 91101
}

// 提审操作
const handleSubmitAudit = (id: number) => {
  submitAuditModalRef.value?.showModal(id)
}

// 驳回操作
const handleOverrule = (id: number) => {
  rejectModalRef.value?.showModal(id, 'overrule')
}

// 拒绝操作
const handleRefuse = (id: number) => {
  rejectModalRef.value?.showModal(id, 'refuse')
}

const getRedNumber = async () => {
  badgeStore.fetchBadgeCounts().then(() => {
    count.value.waiting_count = badgeStore.badgeCounts?.managementProductLabelStatusCount?.waiting_count
  })
}

// 操作成功后的回调
const handleOperationSuccess = async () => {
  // 刷新表格数据
  search()
  // 刷新红点数字
  getRedNumber()
}

// // 许可证和资质弹窗
// const handleViewLicense = (id: number, type: string) => {
//   licenseRef.value.showDrawer(id, type)
// }

const handleFilterScopeChange = () => {
  log('🔄 [Tabs] Change start:', { status: otherParams.value.status })
  logKeys('Before switch originalTableKey', originalTableKey.value)
  logKeys('Before switch tableKey', tableKey.value)
  logKeys('Before switch tableRef.tableKey', tableRef.value?.tableKey)
  logVxe('Before switch')
  // 获取各种下拉数据商品选品状态, 22=商品审核状态, 24=提审人
  getCertificateType()
  console.log('🔄 标签页切换开始:', {
    oldStatus: otherParams.value.status,
  })

  // 当状态改变时，清除审核结果筛选
  // 注意：这里不需要清除status，因为status字段现在直接在formArr中管理

  // 重新生成 formArr
  const newFormArr = generateFormArrByStatus(otherParams.value.status)
  log(
    '[Filter] generateFormArrByStatus -> keys:',
    newFormArr.map((x: any) => x.key),
  )

  // 保留现有的下拉选项数据
  newFormArr.forEach((newItem: any) => {
    const oldItem = formArr.value.find((item: any) => item.key === newItem.key)
    if (oldItem && oldItem.options && oldItem.options.length > 0) {
      newItem.options = oldItem.options
      if (newItem.key === 'brand_id_list') {
        console.log('🔍 [handleFilterScopeChange] 保留品牌选项数据:', {
          key: newItem.key,
          optionsLength: newItem.options.length,
          value: newItem.value,
          oldValue: oldItem.value,
        })
      }
    }
  })

  // 添加审核结果的 onChange 回调
  const auditResultItem = newFormArr.find((item: any) => item.key === 'status')
  if (auditResultItem) {
    auditResultItem.onChange = () => {
      // 审核结果筛选项的值直接在formArr中管理，不需要额外同步到otherParams
      console.log('审核结果筛选项值已更新')
    }
  }

  // 更新 formArr
  formArr.value = newFormArr
  log(
    '[Filter] formArr updated keys:',
    formArr.value.map((x: any) => x.key),
  )

  console.log('🔄 formArr 已更新:', {
    newFormArrCount: newFormArr.length,
    formArrKeys: newFormArr.map((item: any) => item.key),
  })

  // 根据当前标签页配置更新表格列显示
  const config = getCurrentTabConfig(otherParams.value.status)
  log('[Config] current tab key:', otherParams.value.status === null ? 'all' : otherParams.value.status)
  log('[Config] hiddenColumns:', config.columnConfig.hiddenColumns)
  log('[Config] columnOrder:', config.columnConfig.columnOrder)

  // 确保有原始配置可用
  if (originalTableKey.value.length === 0) {
    log('[Baseline] originalTableKey empty, fallback to current tableKey snapshot')
    originalTableKey.value = JSON.parse(JSON.stringify(tableKey.value))
  }
  logKeys('Baseline originalTableKey (pre-filter)', originalTableKey.value)
  logCols('Baseline originalTableKey (detail)', originalTableKey.value)

  // 基于原始完整配置进行过滤，避免基于已修改的状态进行计算
  const visibleColumns = originalTableKey.value.filter((item: any) => !config.columnConfig.hiddenColumns.includes(item.key))
  logKeys('visibleColumns', visibleColumns)

  // 根据配置的顺序重新排序列
  const orderedColumns = config.columnConfig.columnOrder.map((key) => visibleColumns.find((col: any) => col.key === key)).filter(Boolean as any)
  logKeys('orderedColumns', orderedColumns)

  // 添加任何在配置中没有指定但应该显示的列
  const unspecifiedColumns = visibleColumns.filter((col: any) => !config.columnConfig.columnOrder.includes(col.key))
  logKeys('unspecifiedColumns', unspecifiedColumns)

  const newTableKey = [...orderedColumns, ...unspecifiedColumns]
  logKeys('newTableKey (before operate width)', newTableKey)

  // 更新操作列宽度
  const operateColumn = newTableKey.find((item: any) => item.key === 'operate')
  if (operateColumn) {
    operateColumn.width = config.columnConfig.operateWidth
  }
  logCols('newTableKey (after operate width)', newTableKey)

  // 同步更新两个引用，确保状态一致
  tableKey.value = newTableKey
  tableRef.value.tableKey = newTableKey
  logKeys('After set tableKey', tableKey.value)
  logKeys('After set tableRef.tableKey', tableRef.value?.tableKey)

  // 使用多层 nextTick 和智能重试机制确保列配置正确应用
  nextTick(() => {
    nextTick(() => {
      try {
        logVxe('Before refreshColumn (double nextTick)')

        // 首先尝试标准的 refreshColumn
        tableRef.value?.tableRef?.refreshColumn?.()
        log('[VXE] refreshColumn called ✓')

        // 延迟检查并强制同步列配置
        setTimeout(() => {
          const fullColumn = logVxe('After refreshColumn check')

          // 检查 vxe-table 是否真正初始化完成
          const isVxeReady = fullColumn && fullColumn.length > 0 && fullColumn[0] !== undefined && fullColumn[0] !== null && typeof fullColumn[0] === 'object' && fullColumn[0].field !== undefined

          if (isVxeReady) {
            // vxe-table 已正确初始化，使用强制同步函数
            const syncSuccess = forceVxeColumnSync(newTableKey)
            if (syncSuccess) {
              log('[VXE] Column configuration synchronized successfully ✓')
            } else {
              log('[VXE] Column sync failed, will try remount...')
              if (FORCE_REMOUNT_ON_VXE_FAIL) {
                log('[VXE] Forcing BaseTable remount due to sync failure')
                baseTableRenderKey.value++
              }
            }
          } else {
            log('[VXE] vxe-table not properly initialized - fullColumn contains undefined objects')

            if (FORCE_REMOUNT_ON_VXE_FAIL) {
              log('[VXE] Forcing BaseTable remount due to persistent vxe initialization failure')
              baseTableRenderKey.value++
              // 重新挂载后，originalTableKey 会在 handleInitFinish 中重新设置
            } else {
              // 最后一次重试
              setTimeout(() => {
                tableRef.value?.tableRef?.refreshColumn?.()
                logVxe('Final retry refreshColumn')
              }, 200)
            }
          }
        }, 100)
      } catch (e) {
        console.error('[VXE] refreshColumn process failed:', e)
        if (FORCE_REMOUNT_ON_VXE_FAIL) {
          log('[VXE] Forcing BaseTable remount due to exception')
          baseTableRenderKey.value++
        }
      }
    })
  })

  // 如果启用了强制重挂载策略，直接重挂载 BaseTable
  if (FORCE_REMOUNT_ON_SWITCH) {
    log('[VXE] Using FORCE_REMOUNT_ON_SWITCH strategy - remounting BaseTable')
    baseTableRenderKey.value++
    // 重挂载后会自动触发 handleInitFinish，不需要手动调用 search()
    return
  }

  // 触发查询
  search()
  log('当前status:', otherParams.value.status)
  log('当前生效的配置:', config)
}

const handleInitFinish = (val: any) => {
  console.groupCollapsed('%c[Init] BaseTable initFinish', 'color: #1890ff')
  console.log('initFinish called at:', new Date().toISOString())
  logKeys('init val keys', val)
  logCols('init val detail', val)

  // 确保formatter配置正确
  const columnsWithFormatter = ensureFormatterConfig(val)

  // 保存原始完整的表格列配置，用于标签页切换时的基准计算
  if (originalTableKey.value.length === 0) {
    originalTableKey.value = JSON.parse(JSON.stringify(columnsWithFormatter))
    logKeys('saved originalTableKey (first time)', originalTableKey.value)
  } else {
    logKeys('originalTableKey already exists, keeping it', originalTableKey.value)
  }

  // 如果当前不是"全部"标签页，需要立即应用正确的列配置
  const currentStatus = otherParams.value.status
  if (currentStatus !== null) {
    log('[Init] Not on "all" tab, applying column config for status:', currentStatus)

    const config = getCurrentTabConfig(currentStatus)
    const visibleColumns = originalTableKey.value.filter((item: any) => !config.columnConfig.hiddenColumns.includes(item.key))
    const orderedColumns = config.columnConfig.columnOrder.map((key) => visibleColumns.find((col: any) => col.key === key)).filter(Boolean as any)
    const unspecifiedColumns = visibleColumns.filter((col: any) => !config.columnConfig.columnOrder.includes(col.key))
    const newTableKey = [...orderedColumns, ...unspecifiedColumns]

    // 确保formatter配置正确
    ensureFormatterConfig(newTableKey)

    // 更新操作列宽度
    const operateColumn = newTableKey.find((item: any) => item.key === 'operate')
    if (operateColumn) {
      operateColumn.width = config.columnConfig.operateWidth
    }

    tableKey.value = newTableKey
    tableRef.value.tableKey = newTableKey
    logKeys('applied column config in init', newTableKey)
  } else {
    // "全部"标签页，使用完整配置
    tableKey.value = val
    logKeys('using full config for "all" tab', val)
  }

  console.groupEnd()
}
const getCertificateType = async () => {
  const [/* certificateOptions, */ productBrandOptions] = await getCommonOption([/* 11,  */ 19])

  console.log('🔍 [getCertificateType] 获取到的品牌选项:', productBrandOptions)

  // 获取选品状态、审核状态、提审人、选品人数据
  const dropdownRes = await GetCommonOption({ types: [23, 22, 24] }) // 23=商品选品状态, 22=商品审核状态, 24=提审人
  const selectionStatusOptions = dropdownRes.data?.data?.[23] || []
  const submitAuditNameOptions = dropdownRes.data?.data?.[24] || [] // 提审人选项

  formArr.value.forEach((item: any) => {
    // if (item.key === 'supplier_product_status') {
    //   // 状态下拉数据
    //   item.options = certificateOptions
    // }
    if (item.key === 'brand_id_list') {
      // 品牌下拉数据
      item.options = productBrandOptions
      console.log('🔍 [getCertificateType] 品牌筛选项配置:', {
        key: item.key,
        label: item.label,
        value: item.value,
        multiple: item.multiple,
        options: item.options,
        optionsLength: item.options?.length,
      })
    }
    if (item.key === 'selection_status') {
      // 选品状态下拉数据
      item.options = selectionStatusOptions
    }
    if (item.key === 'submit_audit_name') {
      // 提审人下拉数据
      item.options = submitAuditNameOptions
    }
    if (item.key === 'selection_person') {
      // 选品人下拉数据（暂时使用提审人的数据，后续可能需要单独的API）
      item.options = submitAuditNameOptions
    }
  })
}
const getCategoryOption = async () => {
  const res = await GetCategoryOption()
  categoryOptions.value = res.data
  const formatTreeData = (categoryOptions) => {
    return categoryOptions.map((item) => {
      const node = {
        label: item.name,
        value: item.id,
        children: item.children?.length ? formatTreeData(item.children) : undefined,
      }
      return node
    })
  }
  const treeData = formatTreeData(res.data)
  formArr.value.forEach((item) => {
    if (item.key === 'category_id_list') {
      item.options = treeData
    }
  })
}

const changeTimeScope = (value: any) => {
  console.log(value)
  otherParams.value.modified_at_start = value.startDate
  otherParams.value.modified_at_end = value.endDate
  dateRange.value = [value.startDate, value.endDate]
  search()
}
const changeTimeRange = (value: any) => {
  if (value && value.length > 1) {
    otherParams.value.modified_at_start = value[0]
    otherParams.value.modified_at_end = value[1]
  } else {
    otherParams.value.modified_at_start = ''
    otherParams.value.modified_at_end = ''
    quickTimeSelectionRef.value.setTimeScope(0)
  }
  search()
}
const foucsTimeRange = () => {
  otherParams.value.modified_at_start = ''
  otherParams.value.modified_at_end = ''
  quickTimeSelectionRef.value.setTimeScope(0)
}

// 导出
const handleExport = async () => {
  const ids = tableRef.value.checkItemsArr.map((item: any) => item.id)
  const createTime = formArr.value.find((item: any) => item.key === 'create_at')?.value
  const modifiedTime = formArr.value.find((item: any) => item.key === 'modified_at')?.value
  // 根据当前标签页状态设置 page_export_type
  let exportPageType = 1 // 默认为全部页面
  const currentStatus = otherParams.value.status

  if (currentStatus === null) {
    exportPageType = 11 // 全部页面
  } else if (currentStatus === 0) {
    exportPageType = 12 // 待选品
  } else if (currentStatus === 1) {
    exportPageType = 13 // 选品审核中
  } else if (currentStatus === 2) {
    exportPageType = 15 // 已驳回
  } else if (currentStatus === 3) {
    exportPageType = 14 // 完成选品
  } else if (currentStatus === 4) {
    exportPageType = 16 // 已拒绝
  }
  // 构建filter对象
  const filter: any = {
    supplier_product_number: formArr.value.find((item: any) => item.key === 'supplier_product_number')?.value,
    product_number: formArr.value.find((item: any) => item.key === 'product_number')?.value,
    product_name: formArr.value.find((item: any) => item.key === 'product_name')?.value,
    product_name_en: formArr.value.find((item: any) => item.key === 'product_name_en')?.value,
    brand_id_list: formArr.value.find((item: any) => item.key === 'brand_id_list')?.value,
    category_id_list: formArr.value.find((item: any) => item.key === 'category_id_list')?.value,
    status: formArr.value.find((item: any) => item.key === 'status')?.value,
    create_at_start: Array.isArray(createTime) && createTime[0] ? new Date(createTime[0]).toISOString() : undefined,
    create_at_end: Array.isArray(createTime) && createTime[1] ? new Date(createTime[1]).toISOString() : undefined,
    modified_at_start: Array.isArray(modifiedTime) && modifiedTime[0] ? new Date(modifiedTime[0]).toISOString() : undefined,
    modified_at_end: Array.isArray(modifiedTime) && modifiedTime[1] ? new Date(modifiedTime[1]).toISOString() : undefined,
  }

  // 根据当前标签页状态添加selection_status参数（除了"全部"标签页外）
  if (currentStatus !== null) {
    if (currentStatus === 0) {
      // 待选品：selection_status = 0
      filter.selection_status = 0
    } else if (currentStatus === 1) {
      // 选品审核中：selection_status = 10
      filter.selection_status = 10
    } else if (currentStatus === 2) {
      // 已驳回：selection_status = 50
      filter.selection_status = 50
    } else if (currentStatus === 3) {
      // 完成选品：selection_status = 40
      filter.selection_status = 40
    } else if (currentStatus === 4) {
      // 已拒绝：selection_status = 60
      filter.selection_status = 60
    }
  }

  // 处理商品类目参数，将级联选择器的数组数组转换为叶子节点ID数组
  if (filter.category_id_list && Array.isArray(filter.category_id_list)) {
    const leafIds: number[] = []
    filter.category_id_list.forEach((path: number[]) => {
      if (path && path.length > 0) {
        // 取每个路径的最后一个ID（叶子节点）
        leafIds.push(path[path.length - 1])
      }
    })
    filter.category_id_list = leafIds
  }

  const params = {
    export_type: exportSelectValue.value,
    page_export_type: exportPageType,
    ids,
    filter,
  }

  // 处理不同导出类型的参数
  if (exportSelectValue.value == 1) {
    // 导出全部：删除ids，但需要保留非"全部"标签页的selection_status参数
    delete (params as any).ids

    // 如果是"全部"标签页，删除整个filter对象
    if (currentStatus === null) {
      delete (params as any).filter
    } else {
      // 如果是其他标签页，只保留selection_status参数
      const selectionStatusFilter: any = {}
      if (currentStatus === 0) {
        selectionStatusFilter.selection_status = 0 // 待选品
      } else if (currentStatus === 1) {
        selectionStatusFilter.selection_status = 10 // 选品审核中
      } else if (currentStatus === 2) {
        selectionStatusFilter.selection_status = 50 // 已驳回
      } else if (currentStatus === 3) {
        selectionStatusFilter.selection_status = 40 // 完成选品
      } else if (currentStatus === 4) {
        selectionStatusFilter.selection_status = 60 // 已拒绝
      }
      params.filter = selectionStatusFilter
    }
  }
  if (exportSelectValue.value == 2) {
    delete (params as any).filter
    if (ids.length == 0) {
      message.warning('请勾选要导出的商品')
      return
    }
  }
  if (exportSelectValue.value == 3) {
    delete (params as any).ids
  }

  try {
    // 显示导出准备中通知
    // const taskId = `supplier_product_export_${Date.now()}`
    // showExportNotification({
    //   type: 'progress',
    //   title: '导出文件准备中',
    //   message: '正在处理您的商品导出请求，请稍候...',
    //   taskId,
    // })

    // 使用新的下载中心接口
    const downloadParams = {
      file_name: '商品导出',
      export_type_identifier: '5',
      export_params: params as any,
    }

    const res = await AddDownloadTask(downloadParams)
    if (res.success) {
      message.success('导出任务已添加到下载队列，请到下载中心查看进度')
    } else {
      message.error(res.message || '添加导出任务失败')
    }
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

const clearFilter = () => {
  otherParams.value.modified_at_start = ''
  otherParams.value.modified_at_end = ''
  dateRange.value = []
  quickTimeSelectionRef.value.setTimeScope(0)
}

const setPreviewVisible = async (row: any, visible: boolean) => {
  if (visible) {
    try {
      const imgRes = await GetProductPreviewUrl({
        fileId: row.main_images_id,
        width: 10000,
        height: 10000,
      })
      row.bigImagesUrl = imgRes.data.view_url
    } catch (e) {
      row.bigImagesUrl = row.images_view_url
    }
  }
}

// 复制
// const handleCopy = (text: string) => {
//   if (navigator.clipboard && navigator.clipboard.writeText) {
//     navigator.clipboard
//       .writeText(text)
//       .then(() => {
//         message.success('复制成功')
//       })
//       .catch(() => {
//         message.error('复制失败，请手动复制')
//       })
//   } else {
//     // 兼容处理：使用 document.execCommand
//     const input = document.createElement('input')
//     input.value = text
//     document.body.appendChild(input)
//     input.select()
//     try {
//       document.execCommand('copy')
//       message.success('复制成功')
//     } catch (e) {
//       message.error('复制失败，请手动复制')
//     }
//     document.body.removeChild(input)
//   }
// }

// 获取第一个有权限的标签页
const getFirstAvailableTab = () => {
  // 定义标签页的顺序和对应的权限ID
  const tabOrder = [
    { status: null, permissionId: 91100 }, // 全部
    { status: 0, permissionId: 91200 }, // 待选商品
    { status: 1, permissionId: 91300 }, // 选品审核中
    { status: 3, permissionId: 91500 }, // 完成选品
    { status: 2, permissionId: 91400 }, // 已驳回
    { status: 4, permissionId: 91600 }, // 已拒绝
  ]

  // 找到第一个有权限的标签页
  for (const tab of tabOrder) {
    if (btnPermission[tab.permissionId]) {
      console.log('🎯 [getFirstAvailableTab] 找到第一个有权限的标签页:', {
        status: tab.status,
        permissionId: tab.permissionId,
        statusName:
          tab.status === null
            ? '全部'
            : tab.status === 0
              ? '待选商品'
              : tab.status === 1
                ? '选品审核中'
                : tab.status === 2
                  ? '已驳回'
                  : tab.status === 3
                    ? '完成选品'
                    : tab.status === 4
                      ? '已拒绝'
                      : '未知',
      })
      return tab.status
    }
  }

  // 如果没有任何权限，默认返回null（全部标签页）
  console.warn('⚠️ [getFirstAvailableTab] 没有找到任何有权限的标签页，使用默认值null')
  console.warn('⚠️ [getFirstAvailableTab] 当前权限状态:', btnPermission)
  return null
}

// 监听权限数据变化，在权限加载完成后设置默认标签页
watch(
  btnPermission,
  (newPermissions) => {
    // 检查权限对象是否有内容（不是空对象）
    const hasPermissions = Object.keys(newPermissions).length > 0
    if (hasPermissions && !permissionsLoaded.value) {
      console.log('🔑 [Permission] 权限数据已加载:', newPermissions)
      console.log('🔑 [Permission] 当前otherParams.status:', otherParams.value.status)

      // 标记权限已加载
      permissionsLoaded.value = true

      // 设置默认选中第一个有权限的标签页
      const firstAvailableStatus = getFirstAvailableTab()
      console.log('🎯 [Permission] 第一个有权限的标签页:', firstAvailableStatus)

      // 检查当前状态是否有权限，如果没有权限则切换到有权限的标签页
      const currentStatusPermissionId = getCurrentPermissionIdByStatus(otherParams.value.status)
      const hasCurrentPermission = currentStatusPermissionId ? newPermissions[currentStatusPermissionId] : false

      console.log('🔍 [Permission] 当前标签页权限检查:', {
        currentStatus: otherParams.value.status,
        currentPermissionId: currentStatusPermissionId,
        hasCurrentPermission,
        needSwitch: !hasCurrentPermission,
      })

      // 如果当前标签页没有权限，或者当前是默认的null状态但用户没有"全部"权限，则切换
      if (!hasCurrentPermission || (otherParams.value.status === null && !newPermissions[91100])) {
        // 使用 nextTick 确保 DOM 更新
        nextTick(() => {
          otherParams.value.status = firstAvailableStatus
          console.log('✅ [Permission] 标签页状态已更新:', otherParams.value.status)

          // 强制重新渲染标签页组件
          tabsKey.value++
          console.log('🔄 [Permission] 强制重新渲染标签页，tabsKey:', tabsKey.value)

          // 重新生成对应标签页的 formArr
          const newFormArr = generateFormArrByStatus(firstAvailableStatus)
          formArr.value = newFormArr
          console.log('🔧 [Permission] 更新formArr为默认标签页配置:', {
            status: firstAvailableStatus,
            formArrKeys: newFormArr.map((item: any) => item.key),
          })

          // 延迟触发标签页切换事件，确保组件重新渲染完成
          setTimeout(() => {
            handleFilterScopeChange()
          }, 200)
        })
      } else {
        console.log('✅ [Permission] 当前标签页有权限，无需切换')
        // 即使不需要切换，也要确保 formArr 是正确的
        const newFormArr = generateFormArrByStatus(otherParams.value.status)
        formArr.value = newFormArr
      }
    } else if (!hasPermissions) {
      console.log('⏳ [Permission] 权限数据尚未加载完成')
    }
  },
  { immediate: true, deep: true },
)

onMounted(async () => {
  console.groupCollapsed('%c[Mounted] SupplierProductList', 'color: #52c41a')
  console.log('mounted at:', new Date().toISOString())

  getCategoryOption()
  getCertificateType()

  // 获取标签页红点数据
  getRedNumber()

  logKeys('mounted: tableKey', tableKey.value)
  logKeys('mounted: tableRef.tableKey', tableRef.value?.tableKey)
  console.groupEnd()
})
</script>

<style lang="scss" scoped>
// 给小红点（badge count）添加最小宽度30px
:deep(.ant-badge .ant-badge-count.ant-scroll-number) {
  min-width: 30px !important;
}

.image {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

:deep(.ant-badge-count) {
  z-index: 10 !important;
}
/* 红色按钮样式 - 覆盖所有可能的Ant Design样式 */
:deep(.red-text-button) {
  color: #d33333 !important;
}

:deep(.red-text-button.ant-btn) {
  color: #d33333 !important;
  background: transparent !important;
  border: none !important;
}

:deep(.red-text-button.ant-btn.ant-btn-text) {
  color: #d33333 !important;
  background: transparent !important;
  border: none !important;
}

:deep(.red-text-button:hover),
:deep(.red-text-button.ant-btn:hover),
:deep(.red-text-button.ant-btn.ant-btn-text:hover) {
  color: #d33333 !important;
  background: rgb(211 51 51 / 10%) !important;
  border: none !important;
  border-color: transparent !important;
  box-shadow: none !important;
}

:deep(.red-text-button:focus),
:deep(.red-text-button.ant-btn:focus),
:deep(.red-text-button.ant-btn.ant-btn-text:focus) {
  color: #d33333 !important;
  background: transparent !important;
  border: none !important;
  border-color: transparent !important;
  outline: none !important;
  box-shadow: none !important;
}

:deep(.red-text-button:active),
:deep(.red-text-button.ant-btn:active),
:deep(.red-text-button.ant-btn.ant-btn-text:active) {
  color: #d33333 !important;
  background: rgb(211 51 51 / 20%) !important;
  border: none !important;
  border-color: transparent !important;
  box-shadow: none !important;
}

:deep(.red-text-button:disabled),
:deep(.red-text-button.ant-btn:disabled),
:deep(.red-text-button.ant-btn.ant-btn-text:disabled) {
  color: #999 !important;
  background: transparent !important;
  border: none !important;
}
</style>
<style scoped lang="scss">
// 快捷筛选样式 - 复制自Form组件
.quickBox {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
  color: #666;

  .quickLine {
    display: flex;

    .quickLabel {
      box-sizing: border-box;
      padding-top: 3px;
      font-weight: 700;
    }

    .quickContent {
      display: flex;
      flex: 1;
      flex-wrap: wrap;

      .quickItem {
        position: relative;
        height: 24px;
        padding: 3px 8px;
        margin-right: 4px;
        margin-bottom: 8px;
        overflow: hidden;
        font-size: 12px;
        color: #666;
        cursor: pointer;
        background-color: #ebebeb;
        border: 1px solid #ebebeb;
        border-radius: 4px;
        transition: 0.3s all;

        &:hover {
          opacity: 1;

          &.active {
            opacity: 0.7;
          }
        }

        &.active {
          color: #448ef7;
          background: #fff;
          border: 1px solid #448ef7;
          opacity: 1;
        }

        .checkIcon {
          position: absolute;
          top: 0;
          right: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 12px;
          height: 12px;
          color: #fff;
          background: #448ef7;
          border-radius: 0 4px;
          opacity: 0;
          transition: 0.3s all;

          .icon {
            font-size: 8px;
          }
        }
      }
    }
  }
}

// 表单区域间距调整
:deep(.ant-form) {
  margin-bottom: 16px;
}

// 筛选区域与表格的间距
.main {
  :deep(.ant-table-wrapper) {
    margin-top: 16px;
  }
}

// 状态标签页样式优化
.status-radio-group {
  :deep(.ant-radio-button-wrapper) {
    border-radius: 0 !important;
    transition: all 0.3s ease;

    // // 给第一个按钮（全部）添加左上和左下圆角
    // &:first-child {
    //   border-radius: 6px 0 0 6px !important;
    // }

    // // 给最后一个按钮（已拒绝）添加右上和右下圆角
    // &:last-child {
    //   border-radius: 0 6px 6px 0 !important;
    // }
    //  // 强制清除中间按钮的圆角
    // &:not(:first-of-type):not(:last-of-type) {
    //   border-radius: 0 !important;
    // }

    &:hover {
      z-index: 2;
      border-color: #40a9ff;
    }

    &.ant-radio-button-wrapper-checked {
      z-index: 1;
      color: white;
      background: #1890ff;
      border-color: #1890ff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &:focus-within {
      outline: none;
      box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
    }
  }

  // Badge样式调整
  :deep(.ant-badge) {
    .ant-badge-count {
      z-index: 10;
      min-width: 30px;
      height: 16px;
      padding: 0 4px;
      font-size: 11px;
      line-height: 16px;
      border-radius: 8px;
      box-shadow: 0 0 0 1px #fff;
    }
  }
}

// 修复badge层级问题
:deep(.ant-badge-count) {
  z-index: 10 !important;
}

// 时间筛选区域样式
.time-filter-container {
  display: flex;
  gap: 12px; // 元素间间距
  align-items: center; // 垂直居中
  justify-content: center; // 水平居中
  width: 600px; // 占满可用宽度
}

.time-label {
  margin-right: 8px;
  white-space: nowrap; // 防止文字换行
}

.time-controls {
  display: flex;
  gap: 16px; // 快捷时间选择器和日期选择器之间的间距
  align-items: center;
}

// 调整组件内部样式，确保高度一致
:deep(.ant-picker) {
  margin: 0; // 清除默认外边距
}

:deep(.quick-time-selection) {
  // 假设QuickTimeSelection组件有这个类名
  display: flex;
  align-items: center;
}

// 操作按钮样式
.operation-buttons {
  display: flex;
  gap: 8px;
  align-items: center;

  .ant-btn {
    height: 28px;
    padding: 0 12px;
    font-size: 12px;
    border-radius: 4px;

    &.mr-8 {
      margin-right: 8px;
    }

    // 提审按钮样式
    &[type='primary'] {
      background: #1890ff;
      border-color: #1890ff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }

    // 拒绝按钮样式
    &.ant-btn-dangerous {
      color: #ff4d4f;
      border-color: #ff4d4f;

      &:hover {
        color: #fff;
        background: #ff4d4f;
        border-color: #ff4d4f;
      }
    }
  }
}
</style>
